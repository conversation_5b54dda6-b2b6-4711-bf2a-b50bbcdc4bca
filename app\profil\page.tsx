"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { supabase } from "@/lib/supabase";
import { MapPin, Target, Eye, History, Users } from "lucide-react";

interface VillageInfo {
  id: string;
  name: string;
  history: string;
  vision: string;
  mission: string;
  geography: string;
  demographics: any;
}

export default function ProfilPage() {
  const [villageInfo, setVillageInfo] = useState<VillageInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchVillageInfo();
  }, []);

  const fetchVillageInfo = async () => {
    try {
      const { data } = await supabase.from("village_info").select("*").single();

      setVillageInfo(data);
    } catch (error) {
      console.error("Error fetching village info:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat data...</p>
        </div>
      </div>
    );
  }

  if (!villageInfo) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-gray-600 text-lg">
          Data profil Pakan Rabaa Utara Duo tidak ditemukan
        </p>
      </div>
    );
  }

  const demographics = villageInfo.demographics || {};

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Hero Section */}
      <section className="bg-green-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Profil {villageInfo.name}
          </h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto">
            Mengenal lebih dekat sejarah, visi, misi, dan potensi Pakan Rabaa
            Utara Duo kami
          </p>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid gap-8">
          {/* Sejarah */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <History className="w-6 h-6 text-green-600" />
                <span>Sejarah Pakan Rabaa Utara Duo</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                {villageInfo.history}
              </p>
            </CardContent>
          </Card>

          {/* Visi & Misi */}
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center space-x-3">
                  <Eye className="w-6 h-6 text-blue-600" />
                  <span>Visi</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                  {villageInfo.vision}
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center space-x-3">
                  <Target className="w-6 h-6 text-purple-600" />
                  <span>Misi</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                  {villageInfo.mission}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Geografi */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <MapPin className="w-6 h-6 text-red-600" />
                <span>Geografi</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                {villageInfo.geography}
              </p>
            </CardContent>
          </Card>

          {/* Demografi */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <Users className="w-6 h-6 text-indigo-600" />
                <span>Data Demografi</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {demographics.population || 0}
                  </div>
                  <div className="text-gray-600">Total Penduduk</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {demographics.families || 0}
                  </div>
                  <div className="text-gray-600">Kepala Keluarga</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {demographics.males || 0}
                  </div>
                  <div className="text-gray-600">Laki-laki</div>
                </div>
                <div className="text-center p-4 bg-pink-50 rounded-lg">
                  <div className="text-2xl font-bold text-pink-600">
                    {demographics.females || 0}
                  </div>
                  <div className="text-gray-600">Perempuan</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
